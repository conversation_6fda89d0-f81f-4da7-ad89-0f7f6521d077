# 🚀 React Starter Kit

A modern **React Starter Kit** pre-configured with essential tools and libraries for building scalable frontend applications quickly and efficiently.

## 🧩 Tech Stack

- ⚛️ **React** (with Vite)
- 💨 **Tailwind CSS** – Utility-first CSS framework
- 🧱 **shadcn/ui** – Beautifully designed UI components built on Radix and Tailwind
- 🦄 **Lucide React** – Icon library
- 🔀 **React Router DOM** – Routing and navigation
- 🌐 **Axios** – Promise-based HTTP client
- 🧠 **Zustand** – Global state management
- 🧑‍💻 **Tanstack Query** – Data fetching and caching
- 💻**Typescript**- For type safe

---

## ⚙️ Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/Lwant-02/React-Starter-Kit.git
cd react-starter-kit
```

---

### 2. Install the Dependencies

```bash
cd react-starter-kit
npm i
#or
yarn i
```

---

### 3. Run the Development Server

```bash
npm run dev
# or
yarn dev
```
