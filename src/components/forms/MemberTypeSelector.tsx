import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, GraduationCap, Store } from "lucide-react";

type MemberType = "general" | "student" | "store";

interface MemberTypeData {
  type: MemberType;
  title: string;
  description: string;
  icon: typeof User;
  color: string;
  hoverColor: string;
}

interface MemberTypeSelectorProps {
  onSelectType: (type: MemberType) => void;
}

export const MemberTypeSelector = ({ onSelectType }: MemberTypeSelectorProps) => {
  const memberTypes: MemberTypeData[] = [
    {
      type: "general",
      title: "General User",
      description: "Individual customers who want to purchase books",
      icon: User,
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600",
    },
    {
      type: "student",
      title: "Student",
      description: "Students with valid student ID for special discounts",
      icon: GraduationCap,
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600",
    },
    {
      type: "store",
      title: "Store/Business",
      description: "Businesses looking to purchase books in bulk",
      icon: Store,
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 },
    },
  };

  return (
    <motion.div
      key="selection"
      className="grid md:grid-cols-3 gap-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
    >
      {memberTypes.map((type) => {
        const IconComponent = type.icon;
        return (
          <motion.div key={type.type} variants={itemVariants}>
            <Card
              className="cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-2 hover:border-gray-300"
              onClick={() => onSelectType(type.type)}
            >
              <CardHeader className="text-center pb-4">
                <div className={`w-16 h-16 ${type.color} rounded-full flex items-center justify-center mx-auto mb-4 transition-colors ${type.hoverColor}`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl">{type.title}</CardTitle>
                <CardDescription className="text-sm">
                  {type.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button className="w-full" variant="outline">
                  Select {type.title}
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        );
      })}
    </motion.div>
  );
};
