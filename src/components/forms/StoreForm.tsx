import { motion } from "framer-motion";
import { User, Store, IdCard, Mail, Upload } from "lucide-react";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { Button } from "../ui/button";

interface StoreFormProps {
  formData: {
    businessOperatorName?: string;
    businessName?: string;
    legalEntityRegistrationNumber?: string;
    storeEmail?: string;
    legalDocuments?: File | null;
  };
  onInputChange: (
    field: keyof StoreFormProps["formData"],
    value: string
  ) => void;
  onFileChange: (
    field: keyof StoreFormProps["formData"],
    file: File | null
  ) => void;
}

export const StoreForm = ({
  formData,
  onInputChange,
  onFileChange,
}: StoreFormProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="businessOperator" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            Business Operator Name *
          </Label>
          <Input
            id="businessOperator"
            value={formData.businessOperatorName || ""}
            onChange={(e) =>
              onInputChange("businessOperatorName", e.target.value)
            }
            placeholder="Enter operator's name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="businessName" className="flex items-center gap-2">
            <Store className="w-4 h-4" />
            Business Name *
          </Label>
          <Input
            id="businessName"
            value={formData.businessName || ""}
            onChange={(e) => onInputChange("businessName", e.target.value)}
            placeholder="Enter business name"
            required
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label
            htmlFor="registrationNumber"
            className="flex items-center gap-2"
          >
            <IdCard className="w-4 h-4" />
            Legal Entity Registration Number *
          </Label>
          <Input
            id="registrationNumber"
            value={formData.legalEntityRegistrationNumber || ""}
            onChange={(e) =>
              onInputChange("legalEntityRegistrationNumber", e.target.value)
            }
            placeholder="Enter registration number"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="storeEmail" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            Business Email *
          </Label>
          <Input
            id="storeEmail"
            type="email"
            value={formData.storeEmail || ""}
            onChange={(e) => onInputChange("storeEmail", e.target.value)}
            placeholder="Enter business email"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="legalDocuments" className="flex items-center gap-2">
          <Upload className="w-4 h-4" />
          Legal Entity Documents *
        </Label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
          <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600 mb-2">
            Upload your legal entity documents (PDF, DOC, or images)
          </p>
          <Input
            id="legalDocuments"
            type="file"
            accept=".pdf,.doc,.docx,image/*"
            onChange={(e) =>
              onFileChange("legalDocuments", e.target.files?.[0] || null)
            }
            className="hidden"
            required
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById("legalDocuments")?.click()}
          >
            Choose File
          </Button>
          {formData.legalDocuments && (
            <p className="text-sm text-green-600 mt-2">
              ✓ {formData.legalDocuments.name}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};
