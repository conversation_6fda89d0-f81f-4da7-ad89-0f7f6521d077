import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { IdCard } from "lucide-react";

interface GeneralUserFormProps {
  formData: {
    nationalId?: string;
  };
  onInputChange: (field: string, value: string) => void;
}

export const GeneralUserForm = ({ formData, onInputChange }: GeneralUserFormProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="space-y-2">
        <Label htmlFor="nationalId" className="flex items-center gap-2">
          <IdCard className="w-4 h-4" />
          National ID Card Number *
        </Label>
        <Input
          id="nationalId"
          value={formData.nationalId || ""}
          onChange={(e) => onInputChange("nationalId", e.target.value)}
          placeholder="Enter your national ID number"
          required
        />
      </div>
    </motion.div>
  );
};
