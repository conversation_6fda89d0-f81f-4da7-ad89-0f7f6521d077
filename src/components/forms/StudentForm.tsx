import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { GraduationCap, IdCard, Mail, Calendar, Upload } from "lucide-react";

interface StudentFormProps {
  formData: {
    educationalInstitution?: string;
    studentIdNumber?: string;
    email?: string;
    studentIdExpiryDate?: string;
    studentIdImage?: File | null;
  };
  onInputChange: (field: string, value: string) => void;
  onFileChange: (field: string, file: File | null) => void;
}

export const StudentForm = ({ formData, onInputChange, onFileChange }: StudentFormProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="institution" className="flex items-center gap-2">
            <GraduationCap className="w-4 h-4" />
            Educational Institution *
          </Label>
          <Input
            id="institution"
            value={formData.educationalInstitution || ""}
            onChange={(e) => onInputChange("educationalInstitution", e.target.value)}
            placeholder="Enter your school/university name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="studentId" className="flex items-center gap-2">
            <IdCard className="w-4 h-4" />
            Student ID Number *
          </Label>
          <Input
            id="studentId"
            value={formData.studentIdNumber || ""}
            onChange={(e) => onInputChange("studentIdNumber", e.target.value)}
            placeholder="Enter your student ID"
            required
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            value={formData.email || ""}
            onChange={(e) => onInputChange("email", e.target.value)}
            placeholder="Enter your email address"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="studentIdExpiry" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Student ID Expiry Date *
          </Label>
          <Input
            id="studentIdExpiry"
            type="date"
            value={formData.studentIdExpiryDate || ""}
            onChange={(e) => onInputChange("studentIdExpiryDate", e.target.value)}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="studentIdImage" className="flex items-center gap-2">
          <Upload className="w-4 h-4" />
          Student ID Card Image *
        </Label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
          <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600 mb-2">
            Click to upload or drag and drop your student ID card image
          </p>
          <Input
            id="studentIdImage"
            type="file"
            accept="image/*"
            onChange={(e) => onFileChange("studentIdImage", e.target.files?.[0] || null)}
            className="hidden"
            required
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById("studentIdImage")?.click()}
          >
            Choose File
          </Button>
          {formData.studentIdImage && (
            <p className="text-sm text-green-600 mt-2">
              ✓ {formData.studentIdImage.name}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};
