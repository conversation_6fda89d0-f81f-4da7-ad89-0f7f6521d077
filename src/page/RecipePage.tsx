export const RecipePage = () => {
  const recipes = [
    {
      id: 1,
      title: "THANG NOODLE",
      time: "20 mins",
      servings: "4 servings",
      image:
        "https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 2,
      title: "MANGO STICKY RICE",
      time: "25 mins",
      servings: "4 servings",
      image:
        "https://images.unsplash.com/photo-1512058564366-18510be2db19?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 3,
      title: "CORN SPICY SALAD",
      time: "10 mins",
      servings: "2 servings",
      image:
        "https://images.unsplash.com/photo-1547514701-42782101795e?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 4,
      title: "THANG NOODLE",
      time: "20 mins",
      servings: "4 servings",
      image:
        "https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 5,
      title: "MANGO STICKY RICE",
      time: "25 mins",
      servings: "4 servings",
      image:
        "https://images.unsplash.com/photo-1512058564366-18510be2db19?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 6,
      title: "CORN SPICY SALAD",
      time: "10 mins",
      servings: "2 servings",
      image:
        "https://images.unsplash.com/photo-1547514701-42782101795e?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 7,
      title: "MANGO STICKY RICE",
      time: "25 mins",
      servings: "4 servings",
      image:
        "https://images.unsplash.com/photo-1512058564366-18510be2db19?q=80&w=1200&auto=format&fit=crop",
    },
    {
      id: 8,
      title: "CORN SPICY SALAD",
      time: "10 mins",
      servings: "2 servings",
      image:
        "https://images.unsplash.com/photo-1547514701-42782101795e?q=80&w=1200&auto=format&fit=crop",
    },
  ];

  return (
    <main className="min-h-screen w-full  relative overflow-hidden">
      {/* Decorative background */}
      <div className="pointer-events-none absolute inset-0 -z-10">
        {/* soft radial vignette */}
        <div
          className="absolute inset-0 opacity-60"
          style={{
            backgroundImage:
              "radial-gradient(80% 60% at 50% 0%, rgba(255,255,255,0.55) 0%, rgba(255,255,255,0.25) 35%, rgba(0,0,0,0) 70%)",
          }}
          aria-hidden="true"
        />

        {/* utensil doodles - left side */}
        <svg
          className="absolute left-2 top-24 w-40 h-40 text-orange-300/40"
          viewBox="0 0 200 200"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          aria-hidden="true"
        >
          <path d="M30 160c20-35 20-65 0-100" />
          <path d="M50 40v80" />
          <path d="M65 40v80" />
          <path d="M80 40v80" />
          <circle cx="55" cy="30" r="10" />
          <path d="M100 60c0 20 30 20 30 0 0-15-30-15-30 0z" />
          <path d="M115 60v70" />
        </svg>

        {/* sunburst - top right */}
        <svg
          className="absolute right-6 -top-4 w-72 h-72 text-amber-400/40"
          viewBox="0 0 200 200"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          strokeLinecap="round"
          aria-hidden="true"
        >
          <circle cx="100" cy="100" r="28" opacity="0.35" />
          {Array.from({ length: 20 }).map((_, i) => {
            const angle = (i * Math.PI * 2) / 20;
            const x1 = 100 + Math.cos(angle) * 40;
            const y1 = 100 + Math.sin(angle) * 40;
            const x2 = 100 + Math.cos(angle) * 95;
            const y2 = 100 + Math.sin(angle) * 95;
            return <line key={i} x1={x1} y1={y1} x2={x2} y2={y2} />;
          })}
        </svg>
      </div>

      <section className="mx-auto max-w-[1200px] px-6 py-10">
        <div className="mb-6">
          <div className="text-3xl md:text-4xl tracking-wide font-semibold text-red-600">
            RECOMMENDED
          </div>
          <div className="-mt-1 text-2xl italic text-red-400">Recipes</div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {recipes.map((r) => (
            <article
              key={r.id}
              className="group cursor-pointer relative rounded-lg bg-white shadow-md shadow-black/10 overflow-hidden border border-black/5 hover:shadow-xl transition-all duration-300"
            >
              <div className="relative h-44 w-full overflow-hidden">
                <img
                  src={r.image}
                  alt={r.title}
                  className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  loading="lazy"
                />

                <div className="absolute top-2 left-2 rounded bg-red-600/90 px-2 py-1 text-[11px] font-semibold text-white uppercase tracking-wide">
                  Featured
                </div>
              </div>

              <div className="p-3">
                <h3 className="line-clamp-1 text-[13px] font-bold text-gray-800">
                  {r.title}
                </h3>
                <div className="mt-1 flex items-center gap-2 text-[11px] text-gray-500">
                  <span>{r.time}</span>
                  <span className="h-1 w-1 rounded-full bg-gray-300"></span>
                  <span>{r.servings}</span>
                </div>
              </div>
            </article>
          ))}
        </div>
      </section>
    </main>
  );
};
