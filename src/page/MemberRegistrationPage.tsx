import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import {
  User,
  GraduationCap,
  Store,
  Upload,
  Calendar,
  Phone,
  Mail,
  IdCard,
} from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../components/ui/card";

type MemberType = "general" | "student" | "store" | null;

interface FormData {
  // Common fields
  name: string;
  surname: string;
  phoneNumber: string;
  dateOfBirth: string;

  // General user specific
  nationalId?: string;

  // Student specific
  educationalInstitution?: string;
  studentIdNumber?: string;
  studentIdImage?: File | null;
  studentIdExpiryDate?: string;
  email?: string;

  // Store specific
  businessOperatorName?: string;
  businessName?: string;
  legalEntityRegistrationNumber?: string;
  legalDocuments?: File | null;
  storeEmail?: string;
}

export const MemberRegistrationPage = () => {
  const [selectedType, setSelectedType] = useState<MemberType>(null);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    surname: "",
    phoneNumber: "",
    dateOfBirth: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const memberTypes = [
    {
      type: "general" as const,
      title: "General User",
      description: "Individual customers who want to purchase books",
      icon: User,
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600",
    },
    {
      type: "student" as const,
      title: "Student",
      description: "Students with valid student ID for special discounts",
      icon: GraduationCap,
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600",
    },
    {
      type: "store" as const,
      title: "Store/Business",
      description: "Businesses looking to purchase books in bulk",
      icon: Store,
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600",
    },
  ];

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field: keyof FormData, file: File | null) => {
    setFormData((prev) => ({ ...prev, [field]: file }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("Form submitted:", { type: selectedType, data: formData });
    alert("Registration successful!");

    setIsSubmitting(false);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-12 px-4">
      <motion.div
        className="max-w-4xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <motion.div className="text-center mb-12" variants={itemVariants}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Join Our Book Store
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create your account to start purchasing books. Choose your
            membership type to get started.
          </p>
        </motion.div>

        <AnimatePresence mode="wait">
          {!selectedType ? (
            /* Member Type Selection */
            <motion.div
              key="selection"
              className="grid md:grid-cols-3 gap-6"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {memberTypes.map((type) => {
                const IconComponent = type.icon;
                return (
                  <motion.div key={type.type} variants={itemVariants}>
                    <Card
                      className="cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-2 hover:border-gray-300"
                      onClick={() => setSelectedType(type.type)}
                    >
                      <CardHeader className="text-center pb-4">
                        <div
                          className={`w-16 h-16 ${type.color} rounded-full flex items-center justify-center mx-auto mb-4 transition-colors ${type.hoverColor}`}
                        >
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>
                        <CardTitle className="text-xl">{type.title}</CardTitle>
                        <CardDescription className="text-sm">
                          {type.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <Button className="w-full" variant="outline">
                          Select {type.title}
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </motion.div>
          ) : (
            /* Registration Form */
            <motion.div
              key="form"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="shadow-xl">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {(() => {
                        const selectedTypeData = memberTypes.find(
                          (t) => t.type === selectedType
                        );
                        const IconComponent = selectedTypeData?.icon || User;
                        return (
                          <>
                            <div
                              className={`w-10 h-10 ${selectedTypeData?.color} rounded-full flex items-center justify-center`}
                            >
                              <IconComponent className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-2xl">
                                {selectedTypeData?.title} Registration
                              </CardTitle>
                              <CardDescription>
                                Fill in your information to create your account
                              </CardDescription>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedType(null)}
                      className="text-sm"
                    >
                      Back
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Common Fields */}
                    <motion.div
                      className="grid md:grid-cols-2 gap-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <div className="space-y-2">
                        <Label
                          htmlFor="name"
                          className="flex items-center gap-2"
                        >
                          <User className="w-4 h-4" />
                          First Name *
                        </Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) =>
                            handleInputChange("name", e.target.value)
                          }
                          placeholder="Enter your first name"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="surname"
                          className="flex items-center gap-2"
                        >
                          <User className="w-4 h-4" />
                          Last Name *
                        </Label>
                        <Input
                          id="surname"
                          value={formData.surname}
                          onChange={(e) =>
                            handleInputChange("surname", e.target.value)
                          }
                          placeholder="Enter your last name"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div
                      className="grid md:grid-cols-2 gap-4"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <div className="space-y-2">
                        <Label
                          htmlFor="phone"
                          className="flex items-center gap-2"
                        >
                          <Phone className="w-4 h-4" />
                          Phone Number *
                        </Label>
                        <Input
                          id="phone"
                          value={formData.phoneNumber}
                          onChange={(e) =>
                            handleInputChange("phoneNumber", e.target.value)
                          }
                          placeholder="Enter your phone number"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="dob"
                          className="flex items-center gap-2"
                        >
                          <Calendar className="w-4 h-4" />
                          Date of Birth *
                        </Label>
                        <Input
                          id="dob"
                          type="date"
                          value={formData.dateOfBirth}
                          onChange={(e) =>
                            handleInputChange("dateOfBirth", e.target.value)
                          }
                          required
                        />
                      </div>
                    </motion.div>

                    {/* Type-specific Fields */}
                    <AnimatePresence>
                      {selectedType === "general" && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-4"
                        >
                          <div className="space-y-2">
                            <Label
                              htmlFor="nationalId"
                              className="flex items-center gap-2"
                            >
                              <IdCard className="w-4 h-4" />
                              National ID Card Number *
                            </Label>
                            <Input
                              id="nationalId"
                              value={formData.nationalId || ""}
                              onChange={(e) =>
                                handleInputChange("nationalId", e.target.value)
                              }
                              placeholder="Enter your national ID number"
                              required
                            />
                          </div>
                        </motion.div>
                      )}

                      {selectedType === "student" && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-4"
                        >
                          <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label
                                htmlFor="institution"
                                className="flex items-center gap-2"
                              >
                                <GraduationCap className="w-4 h-4" />
                                Educational Institution *
                              </Label>
                              <Input
                                id="institution"
                                value={formData.educationalInstitution || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "educationalInstitution",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter your school/university name"
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label
                                htmlFor="studentId"
                                className="flex items-center gap-2"
                              >
                                <IdCard className="w-4 h-4" />
                                Student ID Number *
                              </Label>
                              <Input
                                id="studentId"
                                value={formData.studentIdNumber || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "studentIdNumber",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter your student ID"
                                required
                              />
                            </div>
                          </div>

                          <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label
                                htmlFor="email"
                                className="flex items-center gap-2"
                              >
                                <Mail className="w-4 h-4" />
                                Email Address *
                              </Label>
                              <Input
                                id="email"
                                type="email"
                                value={formData.email || ""}
                                onChange={(e) =>
                                  handleInputChange("email", e.target.value)
                                }
                                placeholder="Enter your email address"
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label
                                htmlFor="studentIdExpiry"
                                className="flex items-center gap-2"
                              >
                                <Calendar className="w-4 h-4" />
                                Student ID Expiry Date *
                              </Label>
                              <Input
                                id="studentIdExpiry"
                                type="date"
                                value={formData.studentIdExpiryDate || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "studentIdExpiryDate",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label
                              htmlFor="studentIdImage"
                              className="flex items-center gap-2"
                            >
                              <Upload className="w-4 h-4" />
                              Student ID Card Image *
                            </Label>
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                              <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                              <p className="text-sm text-gray-600 mb-2">
                                Click to upload or drag and drop your student ID
                                card image
                              </p>
                              <Input
                                id="studentIdImage"
                                type="file"
                                accept="image/*"
                                onChange={(e) =>
                                  handleFileChange(
                                    "studentIdImage",
                                    e.target.files?.[0] || null
                                  )
                                }
                                className="hidden"
                                required
                              />
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() =>
                                  document
                                    .getElementById("studentIdImage")
                                    ?.click()
                                }
                              >
                                Choose File
                              </Button>
                              {formData.studentIdImage && (
                                <p className="text-sm text-green-600 mt-2">
                                  ✓ {formData.studentIdImage.name}
                                </p>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {selectedType === "store" && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="space-y-4"
                        >
                          <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label
                                htmlFor="businessOperator"
                                className="flex items-center gap-2"
                              >
                                <User className="w-4 h-4" />
                                Business Operator Name *
                              </Label>
                              <Input
                                id="businessOperator"
                                value={formData.businessOperatorName || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "businessOperatorName",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter operator's name"
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label
                                htmlFor="businessName"
                                className="flex items-center gap-2"
                              >
                                <Store className="w-4 h-4" />
                                Business Name *
                              </Label>
                              <Input
                                id="businessName"
                                value={formData.businessName || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "businessName",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter business name"
                                required
                              />
                            </div>
                          </div>

                          <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label
                                htmlFor="registrationNumber"
                                className="flex items-center gap-2"
                              >
                                <IdCard className="w-4 h-4" />
                                Legal Entity Registration Number *
                              </Label>
                              <Input
                                id="registrationNumber"
                                value={
                                  formData.legalEntityRegistrationNumber || ""
                                }
                                onChange={(e) =>
                                  handleInputChange(
                                    "legalEntityRegistrationNumber",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter registration number"
                                required
                              />
                            </div>
                            <div className="space-y-2">
                              <Label
                                htmlFor="storeEmail"
                                className="flex items-center gap-2"
                              >
                                <Mail className="w-4 h-4" />
                                Business Email *
                              </Label>
                              <Input
                                id="storeEmail"
                                type="email"
                                value={formData.storeEmail || ""}
                                onChange={(e) =>
                                  handleInputChange(
                                    "storeEmail",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter business email"
                                required
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label
                              htmlFor="legalDocuments"
                              className="flex items-center gap-2"
                            >
                              <Upload className="w-4 h-4" />
                              Legal Entity Documents *
                            </Label>
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                              <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                              <p className="text-sm text-gray-600 mb-2">
                                Upload your legal entity documents (PDF, DOC, or
                                images)
                              </p>
                              <Input
                                id="legalDocuments"
                                type="file"
                                accept=".pdf,.doc,.docx,image/*"
                                onChange={(e) =>
                                  handleFileChange(
                                    "legalDocuments",
                                    e.target.files?.[0] || null
                                  )
                                }
                                className="hidden"
                                required
                              />
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() =>
                                  document
                                    .getElementById("legalDocuments")
                                    ?.click()
                                }
                              >
                                Choose File
                              </Button>
                              {formData.legalDocuments && (
                                <p className="text-sm text-green-600 mt-2">
                                  ✓ {formData.legalDocuments.name}
                                </p>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Submit Button */}
                    <motion.div
                      className="pt-6 border-t"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <Button
                        type="submit"
                        className="w-full h-12 text-lg"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            Creating Account...
                          </div>
                        ) : (
                          "Create Account"
                        )}
                      </Button>
                    </motion.div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};
