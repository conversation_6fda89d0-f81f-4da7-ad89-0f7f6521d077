import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "../components/ui/button";
import { User, GraduationCap, Store, ArrowLeft } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../components/ui/card";
import { MemberTypeSelector } from "../components/forms/MemberTypeSelector";
import { CommonFields } from "../components/forms/CommonFields";
import { GeneralUserForm } from "../components/forms/GeneralUserForm";
import { StudentForm } from "../components/forms/StudentForm";
import { StoreForm } from "../components/forms/StoreForm";
import { Link } from "react-router-dom";

type MemberType = "general" | "student" | "store" | null;

interface FormData {
  // Common fields
  name: string;
  surname: string;
  phoneNumber: string;
  dateOfBirth: string;

  // General user specific
  nationalId?: string;

  // Student specific
  educationalInstitution?: string;
  studentIdNumber?: string;
  studentIdImage?: File | null;
  studentIdExpiryDate?: string;
  email?: string;

  // Store specific
  businessOperatorName?: string;
  businessName?: string;
  legalEntityRegistrationNumber?: string;
  legalDocuments?: File | null;
  storeEmail?: string;
}

export const MemberRegistrationPage = () => {
  const [selectedType, setSelectedType] = useState<MemberType>(null);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    surname: "",
    phoneNumber: "",
    dateOfBirth: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getMemberTypeData = (type: MemberType) => {
    const typeMap = {
      general: { title: "General User", icon: User, color: "bg-blue-500" },
      student: { title: "Student", icon: GraduationCap, color: "bg-green-500" },
      store: { title: "Store/Business", icon: Store, color: "bg-purple-500" },
    };
    return type ? typeMap[type] : null;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field: keyof FormData, file: File | null) => {
    setFormData((prev) => ({ ...prev, [field]: file }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    await new Promise((resolve) => setTimeout(resolve, 2000));

    alert("Registration successful!");

    setIsSubmitting(false);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-12 px-4">
      <motion.div
        className="max-w-4xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <motion.div className="text-center mb-12" variants={itemVariants}>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Join Our Book Store
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create your account to start purchasing books. Choose your
            membership type to get started.
          </p>
        </motion.div>

        <AnimatePresence mode="wait">
          {!selectedType ? (
            /* Member Type Selection */
            <MemberTypeSelector onSelectType={setSelectedType} />
          ) : (
            /* Registration Form */
            <motion.div
              key="form"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="shadow-xl">
                <CardHeader>
                  <div className="flex items-center justify-between xl:flex-row flex-col">
                    <div className="flex items-center gap-3">
                      {(() => {
                        const selectedTypeData =
                          getMemberTypeData(selectedType);
                        const IconComponent = selectedTypeData?.icon || User;
                        return (
                          <div className="flex xl:flex-row flex-col justify-center items-center gap-3">
                            <div
                              className={`w-10 h-10 ${selectedTypeData?.color} rounded-full flex items-center justify-center`}
                            >
                              <IconComponent className="w-5 h-5 text-white" />
                            </div>
                            <div className="flex flex-col">
                              <CardTitle className="text-2xl">
                                {selectedTypeData?.title} Registration
                              </CardTitle>
                              <CardDescription>
                                Fill in your information to create your account
                              </CardDescription>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedType(null)}
                      className="text-sm xl:mt-0 mt-3"
                    >
                      Back
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Common Fields */}
                    <CommonFields
                      formData={formData}
                      onInputChange={handleInputChange}
                    />

                    {/* Type-specific Fields */}
                    <AnimatePresence>
                      {selectedType === "general" && (
                        <GeneralUserForm
                          formData={formData}
                          onInputChange={handleInputChange}
                        />
                      )}

                      {selectedType === "student" && (
                        <StudentForm
                          formData={formData}
                          onInputChange={handleInputChange}
                          onFileChange={handleFileChange}
                        />
                      )}

                      {selectedType === "store" && (
                        <StoreForm
                          formData={formData}
                          onInputChange={handleInputChange}
                          onFileChange={handleFileChange}
                        />
                      )}
                    </AnimatePresence>

                    {/* Submit Button */}
                    <motion.div
                      className="pt-6 border-t"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <Button
                        type="submit"
                        className="w-full h-11 text-lg border border-black/50 cursor-pointer"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            Creating Account...
                          </div>
                        ) : (
                          "Create Account"
                        )}
                      </Button>
                    </motion.div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
      <Link
        to="/"
        className="flex justify-center items-center mt-7 hover:underline"
      >
        <ArrowLeft className="size-4" />
        Back to Home
      </Link>
    </div>
  );
};
